2025-07-30 12:17:20 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-30 12:17:20 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-30 12:17:20 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 12:17:20 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 12:17:20.741 [INFO] [exchanges.okx_exchange] 🔧 OKX API限制根源修复为2次/秒，确保30+代币健壮启动
2025-07-30 12:17:20.741 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=2次/秒
2025-07-30 12:17:20 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-07-30 12:17:21 [INFO] [OKXExchange] OKX当前账户模式: 2
2025-07-30 12:17:21 [WARNING] [OKXExchange] OKX账户不是跨币种保证金模式，当前模式: 2
2025-07-30 12:17:25 [INFO] [OKXExchange] OKX设置为单向持仓模式
2025-07-30 12:17:25.867 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 12:17:30.879 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 12:17:30.879 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 12:17:35.893 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 12:17:35.893 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 12:17:35 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SPK-USDT
2025-07-30 12:17:35.893 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 12:17:40.866 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 12:17:40.867 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 12:17:45.880 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 12:17:45.880 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 12:17:45 [DEBUG] [OKXExchange] OKX预设置杠杆成功: RESOLV-USDT
2025-07-30 12:17:45.880 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 12:17:50.871 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 12:17:50.871 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 12:17:55.871 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 12:17:55.871 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 12:17:55 [DEBUG] [OKXExchange] OKX预设置杠杆成功: ICNT-USDT
2025-07-30 12:17:55.871 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 12:18:00.888 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 12:18:00.889 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 12:18:05.884 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 12:18:05.885 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 12:18:05 [DEBUG] [OKXExchange] OKX预设置杠杆成功: CAKE-USDT
2025-07-30 12:18:05 [INFO] [OKXExchange] ✅ OKX账户初始化完成
2025-07-30 12:18:10.863 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-07-30 12:18:15.861 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-07-30 12:18:20.869 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 12:18:25.895 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 12:18:30.859 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 12:18:35.872 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 12:18:35.872 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 12:18:35.897 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-30 19:13:45 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-30 19:13:45 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-30 19:13:45 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 19:13:45 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 19:13:45 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-07-30 19:13:47 [INFO] [OKXExchange] OKX当前账户模式: 2
2025-07-30 19:13:47 [WARNING] [OKXExchange] OKX账户不是跨币种保证金模式，当前模式: 2
2025-07-30 19:13:51 [INFO] [OKXExchange] OKX设置为单向持仓模式
2025-07-30 19:14:01 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SPK-USDT
2025-07-30 19:14:11 [DEBUG] [OKXExchange] OKX预设置杠杆成功: RESOLV-USDT
2025-07-30 19:14:21 [DEBUG] [OKXExchange] OKX预设置杠杆成功: ICNT-USDT
2025-07-30 19:14:31 [DEBUG] [OKXExchange] OKX预设置杠杆成功: CAKE-USDT
2025-07-30 19:14:31 [INFO] [OKXExchange] ✅ OKX账户初始化完成
2025-07-30 19:26:49 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-30 19:26:49 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-30 19:26:49 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 19:26:49 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 19:29:11 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-30 19:29:11 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-30 19:29:11 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 19:29:11 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 19:29:11 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 19:29:11 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 19:29:11 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 19:29:11 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 19:29:11 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 19:30:55 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-30 19:30:55 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-30 19:30:55 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 19:30:55 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 19:30:55 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 19:30:55 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 19:30:55 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 19:30:55 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 19:30:55 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 19:35:00 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-30 19:35:00 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-30 19:35:00 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 19:35:00 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 19:35:00 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 19:35:00 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 19:35:00 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 19:40:16 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-30 19:40:16 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-30 19:40:16 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 19:40:16 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 19:40:16 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 19:40:16 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 19:40:16 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 19:44:45 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-30 19:44:45 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-30 19:44:45 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 19:44:45 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 19:44:45 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 19:44:45 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 19:44:45 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 19:45:25 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-30 19:45:25 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-30 19:45:25 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 19:45:25 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 19:45:25 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 19:45:25 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 19:45:25 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 19:57:26 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-30 19:57:26 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-30 19:57:26 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 19:57:26 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 19:59:04 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-30 19:59:04 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-30 19:59:04 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 19:59:04 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 19:59:04 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-07-30 19:59:06 [INFO] [OKXExchange] OKX当前账户模式: 2
2025-07-30 19:59:06 [WARNING] [OKXExchange] OKX账户不是跨币种保证金模式，当前模式: 2
2025-07-30 19:59:10 [INFO] [OKXExchange] OKX设置为单向持仓模式
2025-07-30 19:59:20 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SPK-USDT
2025-07-30 19:59:30 [DEBUG] [OKXExchange] OKX预设置杠杆成功: RESOLV-USDT
2025-07-30 19:59:40 [DEBUG] [OKXExchange] OKX预设置杠杆成功: ICNT-USDT
2025-07-30 19:59:50 [DEBUG] [OKXExchange] OKX预设置杠杆成功: CAKE-USDT
2025-07-30 19:59:50 [INFO] [OKXExchange] ✅ OKX账户初始化完成
2025-07-30 20:04:17 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-30 20:04:17 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-30 20:04:17 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 20:04:17 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 20:04:17 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-07-30 20:04:18 [INFO] [OKXExchange] OKX当前账户模式: 2
2025-07-30 20:04:18 [WARNING] [OKXExchange] OKX账户不是跨币种保证金模式，当前模式: 2
2025-07-30 20:04:22 [INFO] [OKXExchange] OKX设置为单向持仓模式
2025-07-30 20:04:32 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SPK-USDT
2025-07-30 20:04:42 [DEBUG] [OKXExchange] OKX预设置杠杆成功: RESOLV-USDT
2025-07-30 20:04:52 [DEBUG] [OKXExchange] OKX预设置杠杆成功: ICNT-USDT
2025-07-30 20:05:02 [DEBUG] [OKXExchange] OKX预设置杠杆成功: CAKE-USDT
2025-07-30 20:05:02 [INFO] [OKXExchange] ✅ OKX账户初始化完成
2025-07-30 20:05:17 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 20:05:17 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 20:07:17 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-30 20:07:17 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-30 20:07:17 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 20:07:17 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 20:07:17 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-07-30 20:07:19 [INFO] [OKXExchange] OKX当前账户模式: 2
2025-07-30 20:07:19 [WARNING] [OKXExchange] OKX账户不是跨币种保证金模式，当前模式: 2
2025-07-30 20:07:23 [INFO] [OKXExchange] OKX设置为单向持仓模式
2025-07-30 20:07:33 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SPK-USDT
2025-07-30 20:07:43 [DEBUG] [OKXExchange] OKX预设置杠杆成功: RESOLV-USDT
2025-07-30 20:07:53 [DEBUG] [OKXExchange] OKX预设置杠杆成功: ICNT-USDT
2025-07-30 20:08:03 [DEBUG] [OKXExchange] OKX预设置杠杆成功: CAKE-USDT
2025-07-30 20:08:03 [INFO] [OKXExchange] ✅ OKX账户初始化完成
2025-07-30 20:08:18 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 20:08:18 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 20:13:26 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-30 20:13:26 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-30 20:13:26 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 20:13:26 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 20:14:07 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-30 20:14:07 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-30 20:14:07 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 20:14:07 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 20:26:00 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-30 20:26:00 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-30 20:26:00 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 20:26:00 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 20:27:18 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-30 20:27:18 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-30 20:27:18 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 20:27:18 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 20:27:18 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-07-30 20:27:20 [INFO] [OKXExchange] OKX当前账户模式: 2
2025-07-30 20:27:20 [WARNING] [OKXExchange] OKX账户不是跨币种保证金模式，当前模式: 2
2025-07-30 20:27:23 [INFO] [OKXExchange] OKX设置为单向持仓模式
2025-07-30 20:27:33 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SPK-USDT
2025-07-30 20:27:43 [DEBUG] [OKXExchange] OKX预设置杠杆成功: RESOLV-USDT
2025-07-30 20:27:53 [DEBUG] [OKXExchange] OKX预设置杠杆成功: ICNT-USDT
2025-07-30 20:28:03 [DEBUG] [OKXExchange] OKX预设置杠杆成功: CAKE-USDT
2025-07-30 20:28:03 [INFO] [OKXExchange] ✅ OKX账户初始化完成
2025-07-30 20:33:01 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-30 20:33:01 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-30 20:33:01 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 20:33:01 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 20:33:01 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-07-30 20:33:03 [INFO] [OKXExchange] OKX当前账户模式: 2
2025-07-30 20:33:03 [WARNING] [OKXExchange] OKX账户不是跨币种保证金模式，当前模式: 2
2025-07-30 20:33:07 [INFO] [OKXExchange] OKX设置为单向持仓模式
2025-07-30 20:33:17 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SPK-USDT
2025-07-30 20:33:27 [DEBUG] [OKXExchange] OKX预设置杠杆成功: RESOLV-USDT
2025-07-30 20:33:37 [DEBUG] [OKXExchange] OKX预设置杠杆成功: ICNT-USDT
2025-07-30 20:33:47 [DEBUG] [OKXExchange] OKX预设置杠杆成功: CAKE-USDT
2025-07-30 20:33:47 [INFO] [OKXExchange] ✅ OKX账户初始化完成
2025-07-30 20:42:13 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-30 20:42:13 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-30 20:42:13 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 20:42:13 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 20:42:13 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-07-30 20:42:15 [INFO] [OKXExchange] OKX当前账户模式: 2
2025-07-30 20:42:15 [WARNING] [OKXExchange] OKX账户不是跨币种保证金模式，当前模式: 2
2025-07-30 20:42:19 [INFO] [OKXExchange] OKX设置为单向持仓模式
2025-07-30 20:42:29 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SPK-USDT
2025-07-30 20:42:39 [DEBUG] [OKXExchange] OKX预设置杠杆成功: RESOLV-USDT
2025-07-30 20:42:49 [DEBUG] [OKXExchange] OKX预设置杠杆成功: ICNT-USDT
2025-07-30 20:42:59 [DEBUG] [OKXExchange] OKX预设置杠杆成功: CAKE-USDT
2025-07-30 20:42:59 [INFO] [OKXExchange] ✅ OKX账户初始化完成
2025-07-30 20:43:23 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-30 20:43:23 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-30 20:43:23 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 20:43:23 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 20:43:23 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-07-30 20:43:25 [INFO] [OKXExchange] OKX当前账户模式: 2
2025-07-30 20:43:25 [WARNING] [OKXExchange] OKX账户不是跨币种保证金模式，当前模式: 2
2025-07-30 20:43:28 [INFO] [OKXExchange] OKX设置为单向持仓模式
2025-07-30 20:43:38 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SPK-USDT
2025-07-30 20:43:48 [DEBUG] [OKXExchange] OKX预设置杠杆成功: RESOLV-USDT
2025-07-30 20:43:58 [DEBUG] [OKXExchange] OKX预设置杠杆成功: ICNT-USDT
2025-07-30 20:44:08 [DEBUG] [OKXExchange] OKX预设置杠杆成功: CAKE-USDT
2025-07-30 20:44:08 [INFO] [OKXExchange] ✅ OKX账户初始化完成
2025-07-30 20:46:59 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-30 20:46:59 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-30 20:46:59 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 20:46:59 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 20:46:59.349 [INFO] [exchanges.okx_exchange] 🔧 OKX API限制根源修复为2次/秒，确保30+代币健壮启动
2025-07-30 20:46:59.350 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=2次/秒
2025-07-30 20:46:59 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-07-30 20:47:01 [INFO] [OKXExchange] OKX当前账户模式: 2
2025-07-30 20:47:01 [WARNING] [OKXExchange] OKX账户不是跨币种保证金模式，当前模式: 2
2025-07-30 20:47:04 [INFO] [OKXExchange] OKX设置为单向持仓模式
2025-07-30 20:47:04.835 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 20:47:09.823 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 20:47:09.823 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 20:47:14.812 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 20:47:14.812 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 20:47:14 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SPK-USDT
2025-07-30 20:47:14.813 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 20:47:19.835 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 20:47:19.835 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 20:47:24.842 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 20:47:24.842 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 20:47:24 [DEBUG] [OKXExchange] OKX预设置杠杆成功: RESOLV-USDT
2025-07-30 20:47:24.843 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 20:47:29.838 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 20:47:29.838 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 20:47:34.852 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 20:47:34.853 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 20:47:34 [DEBUG] [OKXExchange] OKX预设置杠杆成功: ICNT-USDT
2025-07-30 20:47:34.853 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 20:47:39.861 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 20:47:39.861 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 20:47:44.881 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 20:47:44.881 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 20:47:44 [DEBUG] [OKXExchange] OKX预设置杠杆成功: CAKE-USDT
2025-07-30 20:47:44 [INFO] [OKXExchange] ✅ OKX账户初始化完成
2025-07-30 20:47:49.887 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-07-30 20:47:59.887 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-07-30 20:48:36 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-30 20:48:36 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-30 20:48:36 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 20:48:36 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 20:48:36.053 [INFO] [exchanges.okx_exchange] 🔧 OKX API限制根源修复为2次/秒，确保30+代币健壮启动
2025-07-30 20:48:36.053 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=2次/秒
2025-07-30 20:48:36 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-07-30 20:48:38 [INFO] [OKXExchange] OKX当前账户模式: 2
2025-07-30 20:48:38 [WARNING] [OKXExchange] OKX账户不是跨币种保证金模式，当前模式: 2
2025-07-30 20:48:41 [INFO] [OKXExchange] OKX设置为单向持仓模式
2025-07-30 20:48:41.559 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 20:48:46.585 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 20:48:46.585 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 20:48:51.558 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 20:48:51.558 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 20:48:51 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SPK-USDT
2025-07-30 20:48:51.558 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 20:48:56.572 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 20:48:56.572 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 20:49:01.578 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 20:49:01.579 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 20:49:01 [DEBUG] [OKXExchange] OKX预设置杠杆成功: RESOLV-USDT
2025-07-30 20:49:01.580 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 20:49:06.589 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 20:49:06.590 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 20:49:11.580 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 20:49:11.580 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 20:49:11 [DEBUG] [OKXExchange] OKX预设置杠杆成功: ICNT-USDT
2025-07-30 20:49:11.580 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 20:49:16.603 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 20:49:16.603 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 20:49:21.615 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 20:49:21.615 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 20:49:21 [DEBUG] [OKXExchange] OKX预设置杠杆成功: CAKE-USDT
2025-07-30 20:49:21 [INFO] [OKXExchange] ✅ OKX账户初始化完成
2025-07-30 20:49:26.618 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-07-30 20:49:36.599 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-07-30 20:49:41.615 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 20:49:46.669 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 20:49:51.632 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 20:49:51.632 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 20:49:52.658 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
