2025-07-30 12:18:05.889 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: SPK-USDT_gate_spot
2025-07-30 12:18:05.889 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: SPK-USDT_gate_futures
2025-07-30 12:18:05.889 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: SPK-USDT_bybit_spot
2025-07-30 12:18:05.890 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: SPK-USDT_bybit_futures
2025-07-30 12:18:05.890 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: SPK-USDT_okx_spot
2025-07-30 12:18:05.891 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: SPK-USDT_okx_futures
2025-07-30 12:18:05.891 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: RESOLV-USDT_gate_spot
2025-07-30 12:18:05.891 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: RESOLV-USDT_gate_futures
2025-07-30 12:18:05.891 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: RESOLV-USDT_bybit_spot
2025-07-30 12:18:05.892 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: RESOLV-USDT_bybit_futures
2025-07-30 12:18:05.892 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: RESOLV-USDT_okx_spot
2025-07-30 12:18:05.892 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: RESOLV-USDT_okx_futures
2025-07-30 12:18:05.893 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: ICNT-USDT_gate_spot
2025-07-30 12:18:05.893 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: ICNT-USDT_gate_futures
2025-07-30 12:18:05.893 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: ICNT-USDT_bybit_spot
2025-07-30 12:18:05.893 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: ICNT-USDT_bybit_futures
2025-07-30 12:18:05.894 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: ICNT-USDT_okx_spot
2025-07-30 12:18:05.894 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: ICNT-USDT_okx_futures
2025-07-30 12:18:05.894 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: CAKE-USDT_gate_spot
2025-07-30 12:18:05.894 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: CAKE-USDT_gate_futures
2025-07-30 12:18:05.895 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: CAKE-USDT_bybit_spot
2025-07-30 12:18:05.895 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: CAKE-USDT_bybit_futures
2025-07-30 12:18:05.895 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: CAKE-USDT_okx_spot
2025-07-30 12:18:05.895 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: CAKE-USDT_okx_futures
2025-07-30 12:18:05.896 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: WIF-USDT_gate_spot
2025-07-30 12:18:05.896 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: WIF-USDT_gate_futures
2025-07-30 12:18:05.896 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: WIF-USDT_bybit_spot
2025-07-30 12:18:05.897 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: WIF-USDT_bybit_futures
2025-07-30 12:18:05.897 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: WIF-USDT_okx_spot
2025-07-30 12:18:05.897 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: WIF-USDT_okx_futures
2025-07-30 12:18:05.897 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: AI16Z-USDT_gate_spot
2025-07-30 12:18:05.898 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: AI16Z-USDT_gate_futures
2025-07-30 12:18:05.898 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: AI16Z-USDT_bybit_spot
2025-07-30 12:18:05.898 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: AI16Z-USDT_bybit_futures
2025-07-30 12:18:05.899 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: AI16Z-USDT_okx_spot
2025-07-30 12:18:05.899 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: AI16Z-USDT_okx_futures
2025-07-30 12:18:05.899 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: SOL-USDT_gate_spot
2025-07-30 12:18:05.899 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: SOL-USDT_gate_futures
2025-07-30 12:18:05.900 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: SOL-USDT_bybit_spot
2025-07-30 12:18:05.900 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: SOL-USDT_bybit_futures
2025-07-30 12:18:05.900 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: SOL-USDT_okx_spot
2025-07-30 12:18:05.900 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: SOL-USDT_okx_futures
2025-07-30 12:18:05.900 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: MATIC-USDT_gate_spot
2025-07-30 12:18:05.901 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: MATIC-USDT_gate_futures
2025-07-30 12:18:05.901 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: MATIC-USDT_bybit_spot
2025-07-30 12:18:05.901 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: MATIC-USDT_bybit_futures
2025-07-30 12:18:05.901 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: MATIC-USDT_okx_spot
2025-07-30 12:18:05.902 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: MATIC-USDT_okx_futures
2025-07-30 12:18:30.859 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_ICNT-USDT | 获取合约信息失败，所有重试都失败
2025-07-30 20:46:58.825 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/time'
2025-07-30 20:46:59.086 [ERROR] [exchanges.bybit_exchange] Bybit同步时间异常: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/time'
2025-07-30 20:46:59.346 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/account/wallet-balance?accountType=UNIFIED'
2025-07-30 20:46:59.347 [ERROR] [exchanges.bybit_exchange] 获取Bybit余额失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/account/wallet-balance?accountType=UNIFIED'
2025-07-30 20:47:44.891 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: SPK-USDT_gate_spot
2025-07-30 20:47:44.892 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: SPK-USDT_gate_futures
2025-07-30 20:47:44.893 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: SPK-USDT_bybit_spot
2025-07-30 20:47:44.894 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: SPK-USDT_bybit_futures
2025-07-30 20:47:44.894 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: SPK-USDT_okx_spot
2025-07-30 20:47:44.895 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: SPK-USDT_okx_futures
2025-07-30 20:47:44.895 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: RESOLV-USDT_gate_spot
2025-07-30 20:47:44.896 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: RESOLV-USDT_gate_futures
2025-07-30 20:47:44.897 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: RESOLV-USDT_bybit_spot
2025-07-30 20:47:44.897 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: RESOLV-USDT_bybit_futures
2025-07-30 20:47:44.897 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: RESOLV-USDT_okx_spot
2025-07-30 20:47:44.898 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: RESOLV-USDT_okx_futures
2025-07-30 20:47:44.898 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: ICNT-USDT_gate_spot
2025-07-30 20:47:44.900 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: ICNT-USDT_gate_futures
2025-07-30 20:47:44.900 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: ICNT-USDT_bybit_spot
2025-07-30 20:47:44.901 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: ICNT-USDT_bybit_futures
2025-07-30 20:47:44.901 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: ICNT-USDT_okx_spot
2025-07-30 20:47:44.902 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: ICNT-USDT_okx_futures
2025-07-30 20:47:44.903 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: CAKE-USDT_gate_spot
2025-07-30 20:47:44.903 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: CAKE-USDT_gate_futures
2025-07-30 20:47:44.904 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: CAKE-USDT_bybit_spot
2025-07-30 20:47:44.904 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: CAKE-USDT_bybit_futures
2025-07-30 20:47:44.904 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: CAKE-USDT_okx_spot
2025-07-30 20:47:44.905 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: CAKE-USDT_okx_futures
2025-07-30 20:47:44.906 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: WIF-USDT_gate_spot
2025-07-30 20:47:44.906 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: WIF-USDT_gate_futures
2025-07-30 20:47:44.907 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: WIF-USDT_bybit_spot
2025-07-30 20:47:44.907 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: WIF-USDT_bybit_futures
2025-07-30 20:47:44.907 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: WIF-USDT_okx_spot
2025-07-30 20:47:44.908 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: WIF-USDT_okx_futures
2025-07-30 20:47:44.908 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: AI16Z-USDT_gate_spot
2025-07-30 20:47:44.908 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: AI16Z-USDT_gate_futures
2025-07-30 20:47:44.909 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: AI16Z-USDT_bybit_spot
2025-07-30 20:47:44.909 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: AI16Z-USDT_bybit_futures
2025-07-30 20:47:44.910 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: AI16Z-USDT_okx_spot
2025-07-30 20:47:44.910 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: AI16Z-USDT_okx_futures
2025-07-30 20:47:44.911 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: SOL-USDT_gate_spot
2025-07-30 20:47:44.911 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: SOL-USDT_gate_futures
2025-07-30 20:47:44.912 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: SOL-USDT_bybit_spot
2025-07-30 20:47:44.912 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: SOL-USDT_bybit_futures
2025-07-30 20:47:44.913 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: SOL-USDT_okx_spot
2025-07-30 20:47:44.913 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: SOL-USDT_okx_futures
2025-07-30 20:47:44.914 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: MATIC-USDT_gate_spot
2025-07-30 20:47:44.914 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: MATIC-USDT_gate_futures
2025-07-30 20:47:44.915 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: MATIC-USDT_bybit_spot
2025-07-30 20:47:44.915 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: MATIC-USDT_bybit_futures
2025-07-30 20:47:44.916 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: MATIC-USDT_okx_spot
2025-07-30 20:47:44.916 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: MATIC-USDT_okx_futures
2025-07-30 20:47:45.534 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=SPKUSDT'
2025-07-30 20:47:45.535 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 SPK-USDT: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=SPKUSDT'
2025-07-30 20:47:47.058 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=SPKUSDT'
2025-07-30 20:47:47.058 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 SPK-USDT: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=SPKUSDT'
2025-07-30 20:47:48.542 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=SPKUSDT'
2025-07-30 20:47:48.542 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 SPK-USDT: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=SPKUSDT'
2025-07-30 20:47:48.543 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: bybit_SPK-USDT | 获取合约信息失败，所有重试都失败
2025-07-30 20:47:50.070 [ERROR] [exchanges.bybit_exchange] Bybit同步时间异常: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/time'
2025-07-30 20:47:50.321 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/account/wallet-balance?accountType=UNIFIED'
2025-07-30 20:47:50.322 [ERROR] [exchanges.bybit_exchange] 获取Bybit余额失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/account/wallet-balance?accountType=UNIFIED'
2025-07-30 20:47:51.585 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=RESOLVUSDT'
2025-07-30 20:47:51.586 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 RESOLV-USDT: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=RESOLVUSDT'
2025-07-30 20:47:53.086 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=RESOLVUSDT'
2025-07-30 20:47:53.086 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 RESOLV-USDT: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=RESOLVUSDT'
2025-07-30 20:47:54.606 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=RESOLVUSDT'
2025-07-30 20:47:54.606 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 RESOLV-USDT: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=RESOLVUSDT'
2025-07-30 20:47:54.607 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: bybit_RESOLV-USDT | 获取合约信息失败，所有重试都失败
2025-07-30 20:48:00.630 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=ICNTUSDT'
2025-07-30 20:48:00.631 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 ICNT-USDT: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=ICNTUSDT'
2025-07-30 20:48:02.151 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=ICNTUSDT'
2025-07-30 20:48:02.152 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 ICNT-USDT: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=ICNTUSDT'
2025-07-30 20:48:35.521 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/time'
2025-07-30 20:48:35.782 [ERROR] [exchanges.bybit_exchange] Bybit同步时间异常: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/time'
2025-07-30 20:48:36.048 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/account/wallet-balance?accountType=UNIFIED'
2025-07-30 20:48:36.048 [ERROR] [exchanges.bybit_exchange] 获取Bybit余额失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/account/wallet-balance?accountType=UNIFIED'
2025-07-30 20:49:21.622 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: SPK-USDT_gate_spot
2025-07-30 20:49:21.622 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: SPK-USDT_gate_futures
2025-07-30 20:49:21.623 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: SPK-USDT_bybit_spot
2025-07-30 20:49:21.624 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: SPK-USDT_bybit_futures
2025-07-30 20:49:21.624 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: SPK-USDT_okx_spot
2025-07-30 20:49:21.624 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: SPK-USDT_okx_futures
2025-07-30 20:49:21.625 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: RESOLV-USDT_gate_spot
2025-07-30 20:49:21.625 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: RESOLV-USDT_gate_futures
2025-07-30 20:49:21.626 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: RESOLV-USDT_bybit_spot
2025-07-30 20:49:21.626 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: RESOLV-USDT_bybit_futures
2025-07-30 20:49:21.627 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: RESOLV-USDT_okx_spot
2025-07-30 20:49:21.627 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: RESOLV-USDT_okx_futures
2025-07-30 20:49:21.627 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: ICNT-USDT_gate_spot
2025-07-30 20:49:21.628 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: ICNT-USDT_gate_futures
2025-07-30 20:49:21.628 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: ICNT-USDT_bybit_spot
2025-07-30 20:49:21.629 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: ICNT-USDT_bybit_futures
2025-07-30 20:49:21.629 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: ICNT-USDT_okx_spot
2025-07-30 20:49:21.630 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: ICNT-USDT_okx_futures
2025-07-30 20:49:21.630 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: CAKE-USDT_gate_spot
2025-07-30 20:49:21.630 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: CAKE-USDT_gate_futures
2025-07-30 20:49:21.631 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: CAKE-USDT_bybit_spot
2025-07-30 20:49:21.631 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: CAKE-USDT_bybit_futures
2025-07-30 20:49:21.632 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: CAKE-USDT_okx_spot
2025-07-30 20:49:21.632 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: CAKE-USDT_okx_futures
2025-07-30 20:49:21.633 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: WIF-USDT_gate_spot
2025-07-30 20:49:21.633 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: WIF-USDT_gate_futures
2025-07-30 20:49:21.634 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: WIF-USDT_bybit_spot
2025-07-30 20:49:21.634 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: WIF-USDT_bybit_futures
2025-07-30 20:49:21.634 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: WIF-USDT_okx_spot
2025-07-30 20:49:21.635 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: WIF-USDT_okx_futures
2025-07-30 20:49:21.635 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: AI16Z-USDT_gate_spot
2025-07-30 20:49:21.636 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: AI16Z-USDT_gate_futures
2025-07-30 20:49:21.636 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: AI16Z-USDT_bybit_spot
2025-07-30 20:49:21.637 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: AI16Z-USDT_bybit_futures
2025-07-30 20:49:21.637 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: AI16Z-USDT_okx_spot
2025-07-30 20:49:21.637 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: AI16Z-USDT_okx_futures
2025-07-30 20:49:21.638 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: SOL-USDT_gate_spot
2025-07-30 20:49:21.638 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: SOL-USDT_gate_futures
2025-07-30 20:49:21.639 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: SOL-USDT_bybit_spot
2025-07-30 20:49:21.639 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: SOL-USDT_bybit_futures
2025-07-30 20:49:21.640 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: SOL-USDT_okx_spot
2025-07-30 20:49:21.640 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: SOL-USDT_okx_futures
2025-07-30 20:49:21.641 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: MATIC-USDT_gate_spot
2025-07-30 20:49:21.641 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: MATIC-USDT_gate_futures
2025-07-30 20:49:21.641 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: MATIC-USDT_bybit_spot
2025-07-30 20:49:21.642 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: MATIC-USDT_bybit_futures
2025-07-30 20:49:21.642 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: MATIC-USDT_okx_spot
2025-07-30 20:49:21.643 [ERROR] [core.trading_rules_preloader] ❌ 无法获取交易规则: MATIC-USDT_okx_futures
2025-07-30 20:49:22.290 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=SPKUSDT'
2025-07-30 20:49:22.291 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 SPK-USDT: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=SPKUSDT'
2025-07-30 20:49:23.794 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=SPKUSDT'
2025-07-30 20:49:23.795 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 SPK-USDT: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=SPKUSDT'
2025-07-30 20:49:25.290 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=SPKUSDT'
2025-07-30 20:49:25.291 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 SPK-USDT: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=SPKUSDT'
2025-07-30 20:49:25.292 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: bybit_SPK-USDT | 获取合约信息失败，所有重试都失败
2025-07-30 20:49:26.794 [ERROR] [exchanges.bybit_exchange] Bybit同步时间异常: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/time'
2025-07-30 20:49:27.051 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/account/wallet-balance?accountType=UNIFIED'
2025-07-30 20:49:27.051 [ERROR] [exchanges.bybit_exchange] 获取Bybit余额失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/account/wallet-balance?accountType=UNIFIED'
2025-07-30 20:49:28.318 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=RESOLVUSDT'
2025-07-30 20:49:28.319 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 RESOLV-USDT: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=RESOLVUSDT'
2025-07-30 20:49:29.824 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=RESOLVUSDT'
2025-07-30 20:49:29.824 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 RESOLV-USDT: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=RESOLVUSDT'
2025-07-30 20:49:31.336 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=RESOLVUSDT'
2025-07-30 20:49:31.336 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 RESOLV-USDT: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=RESOLVUSDT'
2025-07-30 20:49:31.337 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: bybit_RESOLV-USDT | 获取合约信息失败，所有重试都失败
2025-07-30 20:49:37.348 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=ICNTUSDT'
2025-07-30 20:49:37.349 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 ICNT-USDT: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=ICNTUSDT'
2025-07-30 20:49:38.848 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=ICNTUSDT'
2025-07-30 20:49:38.849 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 ICNT-USDT: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=ICNTUSDT'
2025-07-30 20:49:40.357 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=ICNTUSDT'
2025-07-30 20:49:40.357 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 ICNT-USDT: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=ICNTUSDT'
2025-07-30 20:49:40.358 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: bybit_ICNT-USDT | 获取合约信息失败，所有重试都失败
2025-07-30 20:49:41.860 [ERROR] [exchanges.bybit_exchange] Bybit同步时间异常: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/time'
2025-07-30 20:49:42.116 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/position/set-leverage'
2025-07-30 20:49:42.116 [ERROR] [core.unified_leverage_manager] ❌ Bybit杠杆设置异常: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/position/set-leverage'
2025-07-30 20:49:43.371 [ERROR] [exchanges.bybit_exchange] Bybit同步时间异常: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/time'
2025-07-30 20:49:43.630 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/position/set-leverage'
2025-07-30 20:49:43.630 [ERROR] [core.unified_leverage_manager] ❌ Bybit杠杆设置异常: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/position/set-leverage'
2025-07-30 20:49:44.880 [ERROR] [exchanges.bybit_exchange] Bybit同步时间异常: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/time'
2025-07-30 20:49:45.136 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/position/set-leverage'
2025-07-30 20:49:45.136 [ERROR] [core.unified_leverage_manager] ❌ Bybit杠杆设置异常: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/position/set-leverage'
2025-07-30 20:49:46.389 [ERROR] [exchanges.bybit_exchange] Bybit同步时间异常: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/time'
2025-07-30 20:49:46.646 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/position/set-leverage'
2025-07-30 20:49:46.647 [ERROR] [core.unified_leverage_manager] ❌ Bybit杠杆设置异常: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/position/set-leverage'
2025-07-30 20:49:47.896 [ERROR] [exchanges.bybit_exchange] Bybit同步时间异常: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/time'
2025-07-30 20:49:48.146 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/position/set-leverage'
2025-07-30 20:49:48.146 [ERROR] [core.unified_leverage_manager] ❌ Bybit杠杆设置异常: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/position/set-leverage'
2025-07-30 20:49:49.411 [ERROR] [exchanges.bybit_exchange] Bybit同步时间异常: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/time'
2025-07-30 20:49:49.683 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/position/set-leverage'
2025-07-30 20:49:49.684 [ERROR] [core.unified_leverage_manager] ❌ Bybit杠杆设置异常: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/position/set-leverage'
2025-07-30 20:49:52.659 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_ICNT-USDT | 获取合约信息失败，所有重试都失败
2025-07-30 20:49:53.293 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=CAKEUSDT'
2025-07-30 20:49:53.294 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 CAKE-USDT: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=CAKEUSDT'
2025-07-30 20:49:54.803 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=CAKEUSDT'
2025-07-30 20:49:54.803 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 CAKE-USDT: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=CAKEUSDT'
2025-07-30 20:49:56.305 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=CAKEUSDT'
2025-07-30 20:49:56.305 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 CAKE-USDT: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=CAKEUSDT'
2025-07-30 20:49:56.306 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: bybit_CAKE-USDT | 获取合约信息失败，所有重试都失败
