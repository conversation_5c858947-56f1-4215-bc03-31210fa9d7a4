2025-07-30 12:17:19.043 [INFO] [exchanges.bybit_exchange] 🔧 Bybit API限制根源修复为4次/秒，确保30+代币健壮启动
2025-07-30 12:17:19.043 [INFO] [exchanges.bybit_exchange] 初始化Bybit交易所接口，API请求限制: 4/秒
2025-07-30 12:17:19.043 [INFO] [exchanges.bybit_exchange] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 12:17:19.049 [INFO] [BybitExchange] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 12:17:19.049 [INFO] [exchanges.bybit_exchange] 🚀 初始化Bybit交易所（统一账户模式）...
2025-07-30 12:17:19.175 [WARNING] [exchanges.bybit_exchange] Bybit服务器时间响应格式异常，使用本地时间
2025-07-30 12:17:19.176 [INFO] [exchanges.bybit_exchange] ✅ Bybit连接成功，服务器时间: *************
2025-07-30 12:17:19.176 [INFO] [exchanges.bybit_exchange] ✅ Bybit使用统一账户模式
2025-07-30 12:17:19.176 [INFO] [exchanges.bybit_exchange] Bybit查询余额: 原始account_type=unified, 强制使用category=UNIFIED
2025-07-30 12:17:20.661 [INFO] [exchanges.bybit_exchange] Bybit服务器时间同步成功，本地时间: *************, 服务器时间: *************, 偏移: -36ms
2025-07-30 12:17:20.740 [INFO] [exchanges.bybit_exchange] Bybit UNIFIED账户总可用余额: $521.04 USD
2025-07-30 12:17:20.740 [INFO] [exchanges.bybit_exchange] Bybit USDT余额详细计算: 钱包余额=520.99, 锁定=0.00, 订单保证金=0.00, 持仓保证金=0.00, 可用余额=520.99
2025-07-30 12:17:20.740 [INFO] [exchanges.bybit_exchange] Bybit账户余额处理完成: 4个币种
2025-07-30 12:17:20.740 [INFO] [exchanges.bybit_exchange] ✅ Bybit初始余额: 520.99 USDT
2025-07-30 12:17:20.740 [INFO] [exchanges.bybit_exchange] ✅ Bybit统一账户模式已激活
2025-07-30 12:17:20.740 [INFO] [exchanges.bybit_exchange] ✅ Bybit交易所初始化完成
2025-07-30 12:18:06.006 [INFO] [exchanges.bybit_exchange] ✅ Bybit合约信息获取成功: SPK-USDT -> 最大杠杆=25.0x
2025-07-30 12:18:08.921 [INFO] [exchanges.bybit_exchange] Bybit查询余额: 原始account_type=unified, 强制使用category=UNIFIED
2025-07-30 12:18:08.994 [INFO] [exchanges.bybit_exchange] Bybit服务器时间同步成功，本地时间: *************, 服务器时间: *************, 偏移: -38ms
2025-07-30 12:18:09.071 [INFO] [exchanges.bybit_exchange] Bybit UNIFIED账户总可用余额: $521.04 USD
2025-07-30 12:18:09.071 [INFO] [exchanges.bybit_exchange] Bybit USDT余额详细计算: 钱包余额=520.99, 锁定=0.00, 订单保证金=0.00, 持仓保证金=0.00, 可用余额=520.99
2025-07-30 12:18:09.071 [INFO] [exchanges.bybit_exchange] Bybit账户余额处理完成: 4个币种
2025-07-30 12:18:11.991 [INFO] [exchanges.bybit_exchange] ✅ Bybit合约信息获取成功: RESOLV-USDT -> 最大杠杆=25.0x
2025-07-30 12:18:16.494 [INFO] [exchanges.bybit_exchange] ✅ Bybit合约信息获取成功: ICNT-USDT -> 最大杠杆=20.0x
2025-07-30 12:18:23.998 [INFO] [exchanges.bybit_exchange] Bybit服务器时间同步成功，本地时间: *************, 服务器时间: 1753870703961, 偏移: -37ms
2025-07-30 12:18:24.073 [INFO] [exchanges.bybit_exchange] ✅ Bybit杠杆状态正常: 110043: leverage not modified
2025-07-30 12:18:25.501 [INFO] [exchanges.bybit_exchange] Bybit服务器时间同步成功，本地时间: 1753870705501, 服务器时间: 1753870705462, 偏移: -39ms
2025-07-30 12:18:25.576 [INFO] [exchanges.bybit_exchange] ✅ Bybit杠杆状态正常: 110043: leverage not modified
2025-07-30 12:18:26.997 [INFO] [exchanges.bybit_exchange] Bybit服务器时间同步成功，本地时间: 1753870706997, 服务器时间: 1753870706961, 偏移: -36ms
2025-07-30 12:18:27.071 [INFO] [exchanges.bybit_exchange] ✅ Bybit杠杆状态正常: 110043: leverage not modified
2025-07-30 12:18:28.500 [INFO] [exchanges.bybit_exchange] Bybit服务器时间同步成功，本地时间: 1753870708500, 服务器时间: 1753870708463, 偏移: -37ms
2025-07-30 12:18:28.575 [INFO] [exchanges.bybit_exchange] ✅ Bybit杠杆状态正常: 110043: leverage not modified
2025-07-30 12:18:29.999 [INFO] [exchanges.bybit_exchange] Bybit服务器时间同步成功，本地时间: 1753870709999, 服务器时间: 1753870709963, 偏移: -36ms
2025-07-30 12:18:30.074 [INFO] [exchanges.bybit_exchange] ✅ Bybit杠杆状态正常: 110043: leverage not modified
2025-07-30 12:18:31.501 [INFO] [exchanges.bybit_exchange] Bybit服务器时间同步成功，本地时间: 1753870711501, 服务器时间: 1753870711464, 偏移: -37ms
2025-07-30 12:18:31.512 [INFO] [exchanges.bybit_exchange] ✅ Bybit合约信息获取成功: CAKE-USDT -> 最大杠杆=25.0x
2025-07-30 12:18:31.576 [INFO] [exchanges.bybit_exchange] ✅ Bybit杠杆状态正常: 110043: leverage not modified
2025-07-30 20:46:56.140 [INFO] [exchanges.bybit_exchange] 🔧 Bybit API限制根源修复为4次/秒，确保30+代币健壮启动
2025-07-30 20:46:56.141 [INFO] [exchanges.bybit_exchange] 初始化Bybit交易所接口，API请求限制: 4/秒
2025-07-30 20:46:56.141 [INFO] [exchanges.bybit_exchange] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 20:46:56.146 [INFO] [BybitExchange] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 20:46:56.147 [INFO] [exchanges.bybit_exchange] 🚀 初始化Bybit交易所（统一账户模式）...
2025-07-30 20:46:58.825 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/time'
2025-07-30 20:46:58.826 [WARNING] [exchanges.bybit_exchange] 获取Bybit服务器时间失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/time'，使用本地时间
2025-07-30 20:46:58.826 [INFO] [exchanges.bybit_exchange] ✅ Bybit连接成功，服务器时间: *************
2025-07-30 20:46:58.826 [INFO] [exchanges.bybit_exchange] ✅ Bybit使用统一账户模式
2025-07-30 20:46:58.827 [INFO] [exchanges.bybit_exchange] Bybit查询余额: 原始account_type=unified, 强制使用category=UNIFIED
2025-07-30 20:46:59.086 [ERROR] [exchanges.bybit_exchange] Bybit同步时间异常: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/time'
2025-07-30 20:46:59.087 [WARNING] [exchanges.bybit_exchange] Bybit时间同步失败，使用本地时间
2025-07-30 20:46:59.346 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/account/wallet-balance?accountType=UNIFIED'
2025-07-30 20:46:59.347 [ERROR] [exchanges.bybit_exchange] 获取Bybit余额失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/account/wallet-balance?accountType=UNIFIED'
2025-07-30 20:46:59.347 [INFO] [exchanges.bybit_exchange] ✅ Bybit初始余额: 0.00 USDT
2025-07-30 20:46:59.347 [INFO] [exchanges.bybit_exchange] ✅ Bybit统一账户模式已激活
2025-07-30 20:46:59.347 [INFO] [exchanges.bybit_exchange] ✅ Bybit交易所初始化完成
2025-07-30 20:47:45.534 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=SPKUSDT'
2025-07-30 20:47:45.535 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 SPK-USDT: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=SPKUSDT'
2025-07-30 20:47:47.058 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=SPKUSDT'
2025-07-30 20:47:47.058 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 SPK-USDT: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=SPKUSDT'
2025-07-30 20:47:48.300 [INFO] [exchanges.bybit_exchange] Bybit查询余额: 原始account_type=unified, 强制使用category=UNIFIED
2025-07-30 20:47:48.542 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=SPKUSDT'
2025-07-30 20:47:48.542 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 SPK-USDT: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=SPKUSDT'
2025-07-30 20:47:50.070 [ERROR] [exchanges.bybit_exchange] Bybit同步时间异常: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/time'
2025-07-30 20:47:50.070 [WARNING] [exchanges.bybit_exchange] Bybit时间同步失败，使用本地时间
2025-07-30 20:47:50.321 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/account/wallet-balance?accountType=UNIFIED'
2025-07-30 20:47:50.322 [ERROR] [exchanges.bybit_exchange] 获取Bybit余额失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/account/wallet-balance?accountType=UNIFIED'
2025-07-30 20:47:51.585 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=RESOLVUSDT'
2025-07-30 20:47:51.586 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 RESOLV-USDT: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=RESOLVUSDT'
2025-07-30 20:47:53.086 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=RESOLVUSDT'
2025-07-30 20:47:53.086 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 RESOLV-USDT: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=RESOLVUSDT'
2025-07-30 20:47:54.606 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=RESOLVUSDT'
2025-07-30 20:47:54.606 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 RESOLV-USDT: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=RESOLVUSDT'
2025-07-30 20:48:00.630 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=ICNTUSDT'
2025-07-30 20:48:00.631 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 ICNT-USDT: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=ICNTUSDT'
2025-07-30 20:48:02.151 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=ICNTUSDT'
2025-07-30 20:48:02.152 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 ICNT-USDT: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=ICNTUSDT'
2025-07-30 20:48:33.940 [INFO] [exchanges.bybit_exchange] 🔧 Bybit API限制根源修复为4次/秒，确保30+代币健壮启动
2025-07-30 20:48:33.941 [INFO] [exchanges.bybit_exchange] 初始化Bybit交易所接口，API请求限制: 4/秒
2025-07-30 20:48:33.942 [INFO] [exchanges.bybit_exchange] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 20:48:33.946 [INFO] [BybitExchange] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 20:48:33.947 [INFO] [exchanges.bybit_exchange] 🚀 初始化Bybit交易所（统一账户模式）...
2025-07-30 20:48:35.521 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/time'
2025-07-30 20:48:35.522 [WARNING] [exchanges.bybit_exchange] 获取Bybit服务器时间失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/time'，使用本地时间
2025-07-30 20:48:35.522 [INFO] [exchanges.bybit_exchange] ✅ Bybit连接成功，服务器时间: *************
2025-07-30 20:48:35.522 [INFO] [exchanges.bybit_exchange] ✅ Bybit使用统一账户模式
2025-07-30 20:48:35.523 [INFO] [exchanges.bybit_exchange] Bybit查询余额: 原始account_type=unified, 强制使用category=UNIFIED
2025-07-30 20:48:35.782 [ERROR] [exchanges.bybit_exchange] Bybit同步时间异常: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/time'
2025-07-30 20:48:35.782 [WARNING] [exchanges.bybit_exchange] Bybit时间同步失败，使用本地时间
2025-07-30 20:48:36.048 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/account/wallet-balance?accountType=UNIFIED'
2025-07-30 20:48:36.048 [ERROR] [exchanges.bybit_exchange] 获取Bybit余额失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/account/wallet-balance?accountType=UNIFIED'
2025-07-30 20:48:36.049 [INFO] [exchanges.bybit_exchange] ✅ Bybit初始余额: 0.00 USDT
2025-07-30 20:48:36.049 [INFO] [exchanges.bybit_exchange] ✅ Bybit统一账户模式已激活
2025-07-30 20:48:36.050 [INFO] [exchanges.bybit_exchange] ✅ Bybit交易所初始化完成
2025-07-30 20:49:22.290 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=SPKUSDT'
2025-07-30 20:49:22.291 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 SPK-USDT: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=SPKUSDT'
2025-07-30 20:49:23.794 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=SPKUSDT'
2025-07-30 20:49:23.795 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 SPK-USDT: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=SPKUSDT'
2025-07-30 20:49:25.290 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=SPKUSDT'
2025-07-30 20:49:25.291 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 SPK-USDT: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=SPKUSDT'
2025-07-30 20:49:26.531 [INFO] [exchanges.bybit_exchange] Bybit查询余额: 原始account_type=unified, 强制使用category=UNIFIED
2025-07-30 20:49:26.794 [ERROR] [exchanges.bybit_exchange] Bybit同步时间异常: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/time'
2025-07-30 20:49:26.794 [WARNING] [exchanges.bybit_exchange] Bybit时间同步失败，使用本地时间
2025-07-30 20:49:27.051 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/account/wallet-balance?accountType=UNIFIED'
2025-07-30 20:49:27.051 [ERROR] [exchanges.bybit_exchange] 获取Bybit余额失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/account/wallet-balance?accountType=UNIFIED'
2025-07-30 20:49:28.318 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=RESOLVUSDT'
2025-07-30 20:49:28.319 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 RESOLV-USDT: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=RESOLVUSDT'
2025-07-30 20:49:29.824 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=RESOLVUSDT'
2025-07-30 20:49:29.824 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 RESOLV-USDT: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=RESOLVUSDT'
2025-07-30 20:49:31.336 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=RESOLVUSDT'
2025-07-30 20:49:31.336 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 RESOLV-USDT: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=RESOLVUSDT'
2025-07-30 20:49:37.348 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=ICNTUSDT'
2025-07-30 20:49:37.349 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 ICNT-USDT: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=ICNTUSDT'
2025-07-30 20:49:38.848 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=ICNTUSDT'
2025-07-30 20:49:38.849 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 ICNT-USDT: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=ICNTUSDT'
2025-07-30 20:49:40.357 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=ICNTUSDT'
2025-07-30 20:49:40.357 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 ICNT-USDT: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=ICNTUSDT'
2025-07-30 20:49:41.860 [ERROR] [exchanges.bybit_exchange] Bybit同步时间异常: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/time'
2025-07-30 20:49:41.861 [WARNING] [exchanges.bybit_exchange] Bybit时间同步失败，使用本地时间
2025-07-30 20:49:42.116 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/position/set-leverage'
2025-07-30 20:49:43.371 [ERROR] [exchanges.bybit_exchange] Bybit同步时间异常: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/time'
2025-07-30 20:49:43.372 [WARNING] [exchanges.bybit_exchange] Bybit时间同步失败，使用本地时间
2025-07-30 20:49:43.630 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/position/set-leverage'
2025-07-30 20:49:44.880 [ERROR] [exchanges.bybit_exchange] Bybit同步时间异常: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/time'
2025-07-30 20:49:44.880 [WARNING] [exchanges.bybit_exchange] Bybit时间同步失败，使用本地时间
2025-07-30 20:49:45.136 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/position/set-leverage'
2025-07-30 20:49:46.389 [ERROR] [exchanges.bybit_exchange] Bybit同步时间异常: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/time'
2025-07-30 20:49:46.390 [WARNING] [exchanges.bybit_exchange] Bybit时间同步失败，使用本地时间
2025-07-30 20:49:46.646 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/position/set-leverage'
2025-07-30 20:49:47.896 [ERROR] [exchanges.bybit_exchange] Bybit同步时间异常: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/time'
2025-07-30 20:49:47.897 [WARNING] [exchanges.bybit_exchange] Bybit时间同步失败，使用本地时间
2025-07-30 20:49:48.146 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/position/set-leverage'
2025-07-30 20:49:49.411 [ERROR] [exchanges.bybit_exchange] Bybit同步时间异常: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/time'
2025-07-30 20:49:49.412 [WARNING] [exchanges.bybit_exchange] Bybit时间同步失败，使用本地时间
2025-07-30 20:49:49.683 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/position/set-leverage'
2025-07-30 20:49:53.293 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=CAKEUSDT'
2025-07-30 20:49:53.294 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 CAKE-USDT: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=CAKEUSDT'
2025-07-30 20:49:54.803 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=CAKEUSDT'
2025-07-30 20:49:54.803 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 CAKE-USDT: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=CAKEUSDT'
2025-07-30 20:49:56.305 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=CAKEUSDT'
2025-07-30 20:49:56.305 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 CAKE-USDT: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/instruments-info?category=linear&symbol=CAKEUSDT'
